from fastapi import FastAPI, File, UploadFile, Form, HTTPException
from fastapi.responses import JSONResponse
import os
import threading
import faiss
import json
import logging
import numpy as np
from typing import List, Dict, Any, Optional

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler("api.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("image-classification-api")

app = FastAPI(
    title="品类识别API",
    description="基于深度学习的视觉品类识别系统",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# 全局配置
CONFIG = {
    "model_path": "./checkpoint/dinov2_vits14_cpu_scripted.pt",
    "index_path": "./faisslight.index",
    "label_map_path": "./label_map.json",
    "device": "cpu",
    "feature_dim": 384  # DINOv2 vits14的特征维度
}

# 全局锁用于索引操作
index_lock = threading.Lock()

def initialize_system():
    """初始化索引和标签映射文件"""
    if not os.path.exists(CONFIG["index_path"]):
        logger.info("索引文件不存在，创建新索引")
        index = faiss.IndexFlatIP(CONFIG["feature_dim"])
        faiss.write_index(index, CONFIG["index_path"])
    
    if not os.path.exists(CONFIG["label_map_path"]):
        logger.info("标签映射文件不存在，创建新文件")
        with open(CONFIG["label_map_path"], 'w') as f:
            json.dump({}, f)

@app.on_event("startup")
async def startup_event():
    """应用启动时初始化"""
    logger.info("应用启动中...")
    initialize_system()
    logger.info("系统初始化完成，服务已就绪")

# ======================= 核心功能函数 =======================

def load_model():
    """加载DINOv2模型"""
    import torch
    import torch.nn.functional as F
    from torchvision import transforms
    
    device = torch.device(CONFIG["device"])
    model = torch.jit.load(CONFIG["model_path"])
    model = model.to(device)
    model.eval()
    logger.info("模型加载完成")
    return model

def get_transforms(input_size=224):
    """获取图像预处理流程"""
    from torchvision import transforms
    preprocess = transforms.Compose([
        transforms.Resize(input_size + 32),
        transforms.CenterCrop(input_size),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.5]*3, std=[0.5]*3),
    ])
    return preprocess

def query_image(img_path: str, top_k: int = 1) -> Dict[str, Any]:
    """查询图像分类结果"""
    import torch
    import torch.nn.functional as F
    from PIL import Image
    
    try:
        # 加载模型和索引
        model = load_model()
        device = torch.device(CONFIG["device"])
        index = faiss.read_index(CONFIG["index_path"])
        
        # 加载并预处理图像
        img_pil = Image.open(img_path).convert('RGB')
        preprocess = get_transforms(224)
        tensor = preprocess(img_pil).unsqueeze(0).to(device)
        
        # 提取特征
        with torch.no_grad():
            qv = model(tensor)
            qv = F.normalize(qv, dim=1).cpu().numpy()
        
        # 在索引中搜索
        D, I = index.search(qv, top_k)
        
        # 加载标签映射
        with open(CONFIG["label_map_path"], "r", encoding="utf-8") as f:
            name_to_idx = json.load(f)
        idx_to_name = {int(v): k for k, v in name_to_idx.items()}
        
        # 准备结果
        results = []
        for i in range(top_k):
            label_idx = int(I[0][i])
            results.append({
                "label": idx_to_name.get(label_idx, "未知"),
                "score": float(D[0][i]),
                "label_id": label_idx
            })
        
        return {"results": results, "top_k": top_k, "score": float(D[0][i])}
    
    except Exception as e:
        logger.error(f"查询失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"查询失败: {str(e)}")

class AttnPool:
    """注意力池化层"""
    def __init__(self, dim, hidden=128):
        import torch.nn as nn
        self.attn = nn.Sequential(
            nn.Linear(dim, hidden),
            nn.ReLU(),
            nn.Linear(hidden, 1)
        )
    
    def __call__(self, feats):
        import torch.nn.functional as F
        scores = self.attn(feats)
        weights = F.softmax(scores, dim=0)
        return (weights * feats).sum(0)

def add_new_class(image_dir: str, label_name: str, aggregation: str = 'attn') -> Dict[str, Any]:
    """添加新类别到索引"""
    import torch
    import torch.nn.functional as F
    from PIL import Image
    import tqdm
    
    try:
        # 加载模型
        model = load_model()
        device = torch.device(CONFIG["device"])
        preprocess = get_transforms(224)
        
        # 收集所有图像路径
        image_paths = [
            os.path.join(image_dir, f) 
            for f in os.listdir(image_dir) 
            if f.lower().endswith(('.jpg', '.png', '.jpeg'))
        ]
        
        if not image_paths:
            raise ValueError(f"目录中没有图像文件: {image_dir}")
        
        logger.info(f"正在处理 {len(image_paths)} 张图像...")
        
        # 提取特征
        feats = []
        for path in tqdm.tqdm(image_paths):
            img = Image.open(path).convert('RGB')
            tensor = preprocess(img).unsqueeze(0).to(device)
            logger.info(f"测试1")
            with torch.no_grad():
                feat = model(tensor)
                feat = F.normalize(feat, dim=1)
            logger.info(f"测试2")
            feats.append(feat.cpu().numpy()[0])
            logger.info(f"测试3")
        feats = np.vstack(feats)
        logger.info(f"特征提取完成，形状: {feats.shape}")
        
        # 特征聚合
        if aggregation == 'mean':
            vec = feats.mean(0)
        elif aggregation == 'gem':
            vec = np.power(np.mean(np.power(np.clip(feats, a_min=1e-6, a_max=1e4), 3.0), axis=0), 1.0 / 3.0)
        elif aggregation == 'attn':
            import torch
            feats_tensor = torch.tensor(feats, dtype=torch.float32, device=device)
            pool = AttnPool(feats.shape[1])
            vec = pool(feats_tensor).detach().cpu().numpy()
        else:
            raise ValueError(f"不支持的聚合方法: {aggregation}")
        
        vec = vec.reshape(1, -1).astype('float32')
        logger.info(f"聚合向量形状: {vec.shape}")
        
        # 加载索引
        index = faiss.read_index(CONFIG["index_path"])
        
        # 更新标签映射
        with open(CONFIG["label_map_path"], 'r') as f:
            label_map = json.load(f)
        
        # 检查标签是否已存在
        if label_name in label_map:
            label_id = int(label_map[label_name])
            logger.warning(f"标签 '{label_name}' 已存在 (ID: {label_id})，将更新特征向量")
        else:
            # 分配新ID
            existing_ids = [int(id) for id in label_map.values()]
            label_id = max(existing_ids) + 1 if existing_ids else 0
            label_map[label_name] = label_id
            logger.info(f"创建新标签: '{label_name}' -> ID: {label_id}")
        
        # 更新索引
        if index.ntotal > label_id:
            # 替换现有向量
            index.remove_ids(np.array([label_id]))
            index.add_with_ids(vec, np.array([label_id]))
            logger.info(f"替换ID {label_id} 的特征向量")
        else:
            # 添加新向量
            index.add_with_ids(vec, np.array([label_id]))
            logger.info(f"添加新向量到索引 (ID: {label_id})")
        
        # 保存更新
        with open(CONFIG["label_map_path"], 'w') as f:
            json.dump(label_map, f, indent=2)
        
        faiss.write_index(index, CONFIG["index_path"])
        
        return {
            "status": "success",
            "label": label_name,
            "label_id": label_id,
            "num_images": len(image_paths),
            "aggregation": aggregation
        }
    
    except Exception as e:
        logger.error(f"添加类别失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"添加类别失败: {str(e)}")

# ======================= API 端点 =======================

@app.post("/query", 
          summary="图像分类查询", 
          response_description="分类结果",
          tags=["分类"])
async def classify_image(
    image: UploadFile = File(..., description="要分类的图像文件"),
    top_k: int = Form(1, description="返回前K个结果", ge=1, le=10),
    score: float = Form(1, description="返回前K个分数", ge=1, le=10)
):
    """
    对上传的图像进行分类，返回最可能的类别和置信度分数。
    
    - **image**: 上传的图像文件 (JPG/PNG格式)
    - **top_k**: 返回前K个分类结果 (默认1, 最大10)
    - **score**: 返回前K个置信分数 (默认1, 最大10)
    """
    try:
        # 保存临时文件
        temp_path = f"./temp_query_{os.urandom(4).hex()}.jpg"
        with open(temp_path, "wb") as f:
            f.write(await image.read())
        
        # 执行查询
        result = query_image(temp_path, top_k)
        
        return JSONResponse(content=result)
    
    except Exception as e:
        logger.error(f"分类请求失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
    finally:
        # 清理临时文件
        if os.path.exists(temp_path):
            os.remove(temp_path)

@app.post("/add_class", 
          summary="添加新类别", 
          response_description="添加结果",
          tags=["管理"])
async def add_class(
    label_name: str = Form(..., description="新类别的名称"),
    images: List[UploadFile] = File(..., description="该类别的示例图像"),
    aggregation: str = Form('attn', description="特征聚合方法 (mean/gem/attn)")
):
    """
    添加一个新的图像类别到分类系统。
    
    - **label_name**: 新类别的名称
    - **images**: 该类别的示例图像 (至少1张)
    - **aggregation**: 特征聚合方法 (mean-平均, gem-GEM池化, attn-注意力池化)
    """
    if not label_name:
        raise HTTPException(status_code=400, detail="标签名称不能为空")
    
    if not images:
        raise HTTPException(status_code=400, detail="至少需要上传一张图像")
    
    try:
        # 创建临时目录
        temp_dir = f"./temp_{label_name}_{os.urandom(4).hex()}"
        os.makedirs(temp_dir, exist_ok=True)
        
        # 保存所有上传的图像
        for i, img in enumerate(images):
            file_path = os.path.join(temp_dir, f"img_{i}.{img.filename.split('.')[-1]}")
            with open(file_path, "wb") as f:
                f.write(await img.read())
        
        # 加锁执行索引更新
        with index_lock:
            result = add_new_class(temp_dir, label_name, aggregation)
        
        return JSONResponse(content=result)
    
    except Exception as e:
        logger.error(f"添加类别失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
    finally:
        # 清理临时目录
        if os.path.exists(temp_dir):
            for f in os.listdir(temp_dir):
                os.remove(os.path.join(temp_dir, f))
            os.rmdir(temp_dir)

@app.get("/labels", 
         summary="获取所有类别", 
         response_description="类别列表",
         tags=["管理"])
async def get_labels():
    """获取当前系统中所有的类别标签"""
    try:
        with open(CONFIG["label_map_path"], "r") as f:
            label_map = json.load(f)
        
        # 转换为ID排序的列表
        labels = [{"label": k, "id": v} for k, v in label_map.items()]
        labels.sort(key=lambda x: int(x["id"]))
        
        return JSONResponse(content={
            "count": len(labels),
            "labels": labels
        })
    
    except Exception as e:
        logger.error(f"获取标签失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.delete("/label/{label_name}", 
            summary="删除类别", 
            response_description="删除结果",
            tags=["管理"])
async def delete_label(label_name: str):
    """从系统中删除指定类别"""
    try:
        with index_lock:
            # 加载标签映射
            with open(CONFIG["label_map_path"], "r") as f:
                label_map = json.load(f)
            
            if label_name not in label_map:
                raise HTTPException(status_code=404, detail=f"标签 '{label_name}' 不存在")
            
            label_id = int(label_map[label_name])
            
            # 从映射中删除
            del label_map[label_name]
            with open(CONFIG["label_map_path"], "w") as f:
                json.dump(label_map, f, indent=2)
            
            # 从索引中删除
            index = faiss.read_index(CONFIG["index_path"])
            index.remove_ids(np.array([label_id]))
            faiss.write_index(index, CONFIG["index_path"])
            
            logger.info(f"删除标签 '{label_name}' (ID: {label_id})")
            
            return JSONResponse(content={
                "status": "success",
                "deleted_label": label_name,
                "deleted_id": label_id
            })
    
    except Exception as e:
        logger.error(f"删除标签失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/system_status", 
         summary="系统状态", 
         response_description="系统状态信息",
         tags=["管理"])
async def system_status():
    """获取系统当前状态信息"""
    try:
        # 索引信息
        index = faiss.read_index(CONFIG["index_path"])
        index_size = os.path.getsize(CONFIG["index_path"]) / (1024 * 1024)  # MB
        
        # 标签信息
        with open(CONFIG["label_map_path"], "r") as f:
            label_map = json.load(f)
        
        # 模型信息
        model_size = os.path.getsize(CONFIG["model_path"]) / (1024 * 1024)  # MB
        
        return JSONResponse(content={
            "index": {
                "path": CONFIG["index_path"],
                "size_mb": round(index_size, 2),
                "num_vectors": index.ntotal,
                "dimension": index.d
            },
            "labels": {
                "count": len(label_map),
                "path": CONFIG["label_map_path"]
            },
            "model": {
                "path": CONFIG["model_path"],
                "size_mb": round(model_size, 2),
                "device": CONFIG["device"]
            }
        })
    
    except Exception as e:
        logger.error(f"获取系统状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    import uvicorn
    initialize_system()
    uvicorn.run(app, host="0.0.0.0", port=5000, log_level="info")