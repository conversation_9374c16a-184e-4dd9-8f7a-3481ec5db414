1. 安装依赖
bash
pip install fastapi uvicorn python-multipart faiss-cpu torch torchvision pillow tqdm
2. 文件结构
/project
├── app.py                 # 主应用文件
├── checkpoint
│   └── dinov2_vits14_cpu_scripted.pt  # DINOv2模型
├── faisslight.index       # Faiss索引文件 (自动创建)
├── label_map.json         # 标签映射文件 (自动创建)
├── requirements.txt       # 依赖列表
└── temp/                  # 临时文件目录 (自动创建)
3. 启动服务
bash
uvicorn app:app --host 0.0.0.0 --port 5000
4. 访问API文档
在浏览器中打开：http://localhost:5001/docs

使用示例
添加新类别
bash
curl -X POST "http://localhost:5000/add_class" \
  -H "Content-Type: multipart/form-data" \
  -F "label_name=cat" \
  -F "aggregation=attn" \
  -F "images=@cat1.jpg" \
  -F "images=@cat2.jpg"
图像分类
bash
curl -X POST "http://localhost:5000/query" \
  -H "Content-Type: multipart/form-data" \
  -F "image=@test.jpg"
获取所有标签
bash
curl -X GET "http://localhost:5000/labels"